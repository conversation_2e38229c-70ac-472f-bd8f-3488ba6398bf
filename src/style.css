@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
}

/* Element Plus 主题定制 */
:root {
  --el-color-primary: rgb(59, 130, 246);
  --el-color-primary-light-3: rgb(147, 197, 253);
  --el-color-primary-light-5: rgb(191, 219, 254);
  --el-color-primary-light-7: rgb(219, 234, 254);
  --el-color-primary-light-8: rgb(239, 246, 255);
  --el-color-primary-light-9: rgb(248, 250, 252);
  --el-color-primary-dark-2: rgb(37, 99, 235);
  --el-border-radius-base: 8px;
  --el-border-radius-small: 6px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;
}

.dark {
  --el-bg-color: rgb(15, 23, 42);
  --el-bg-color-page: rgb(30, 41, 59);
  --el-bg-color-overlay: rgb(51, 65, 85);
  --el-text-color-primary: rgb(241, 245, 249);
  --el-text-color-regular: rgb(203, 213, 225);
  --el-text-color-secondary: rgb(156, 163, 175);
  --el-text-color-placeholder: rgb(107, 114, 128);
  --el-text-color-disabled: rgb(75, 85, 99);
  --el-border-color: rgb(75, 85, 99);
  --el-border-color-light: rgb(71, 85, 105);
  --el-border-color-lighter: rgb(51, 65, 85);
  --el-border-color-extra-light: rgb(30, 41, 59);
  --el-fill-color: rgb(51, 65, 85);
  --el-fill-color-light: rgb(71, 85, 105);
  --el-fill-color-lighter: rgb(75, 85, 99);
  --el-fill-color-extra-light: rgb(30, 41, 59);
  --el-fill-color-blank: transparent;
  --el-fill-color-dark: rgb(15, 23, 42);
}

/* 自定义组件样式 */
@layer components {
  .glass-effect {
    @apply backdrop-blur-xl bg-white/80 dark:bg-dark-surface/80;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg dark:hover:shadow-2xl hover:-translate-y-1;
  }

  .button-primary {
    @apply inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105;
  }

  .button-secondary {
    @apply inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200;
  }

  .input-modern {
    @apply w-full px-4 py-3 border-2 border-gray-200 dark:border-dark-border rounded-xl bg-white dark:bg-dark-surface text-gray-900 dark:text-dark-text placeholder-gray-500 dark:placeholder-dark-text-secondary focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200;
  }

  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-success {
    @apply status-badge bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300;
  }

  .status-warning {
    @apply status-badge bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300;
  }

  .status-error {
    @apply status-badge bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300;
  }

  .status-info {
    @apply status-badge bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300;
  }
}

/* 动画效果 */
@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 3s infinite;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(243, 244, 246);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-track {
  background: rgb(30, 41, 59);
}

::-webkit-scrollbar-thumb {
  background: rgb(203, 213, 225);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(75, 85, 99);
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(156, 163, 175);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(107, 114, 128);
}

/* Element Plus 组件定制 */
.el-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.el-button:hover {
  transform: translateY(-1px) !important;
}

.el-card {
  border-radius: 12px !important;
  border: 1px solid rgb(229, 231, 235) !important;
  transition: all 0.3s ease !important;
}

.dark .el-card {
  border-color: rgb(75, 85, 99) !important;
  background: rgb(30, 41, 59) !important;
}

.el-card:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-2px) !important;
}

.dark .el-card:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
}

.el-table {
  border-radius: 12px !important;
  overflow: hidden !important;
}

.el-table__header-wrapper {
  border-radius: 12px 12px 0 0 !important;
}

.el-table__body-wrapper {
  border-radius: 0 0 12px 12px !important;
}

.el-dialog {
  border-radius: 16px !important;
  overflow: hidden !important;
}

.el-input__wrapper {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.el-input__wrapper:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* 加载动画 */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
