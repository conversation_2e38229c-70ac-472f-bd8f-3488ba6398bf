<template>
  <el-dialog
    v-model="dialogVisible"
    width="1000px"
    :before-close="handleClose"
    :show-close="false"
    class="modern-dialog"
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-dark-text">创建抠图任务</h3>
            <p class="text-sm text-gray-600 dark:text-dark-text-secondary">选择图片进行AI智能抠图处理</p>
          </div>
        </div>
        <button @click="handleClose"
          class="p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </template>

    <!-- 上传和图库选择按钮 -->
    <div class="p-6 border-b border-gray-100 dark:border-dark-border">
      <div class="flex space-x-4">
        <button @click="triggerUpload"
          class="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          上传图片
        </button>
        <button @click="showGalleryDialog = true"
          class="flex-1 flex items-center justify-center px-6 py-3 bg-white dark:bg-dark-card border-2 border-pink-200 dark:border-pink-800 text-pink-600 dark:text-pink-400 font-medium rounded-lg hover:bg-pink-50 dark:hover:bg-pink-900/20 transition-all duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          从图库选择
        </button>
      </div>
    </div>

    <!-- 图片展示区域 -->
    <div class="bg-gray-50 dark:bg-dark-card border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg min-h-[400px] p-6">
      <div v-if="selectedImages.length > 0">
        <!-- 图片网格 -->
        <div class="grid grid-cols-6 gap-4">
          <div v-for="(image, index) in currentPageImages" :key="image.id || index"
               class="relative group">
            <div class="relative">
              <img :src="image.url" :alt="image.name"
                   class="w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border">
              <button @click="removeImage(index)"
                class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            <p class="text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate">{{ image.name }}</p>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="mt-6 flex justify-center">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="selectedImages.length"
            layout="prev, pager, next"
            small
            @current-change="handlePageChange"
          />
        </div>
      </div>
      <div v-else class="flex flex-col items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
        <svg class="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <p class="text-lg font-medium mb-2">选择要抠图的图片</p>
        <p class="text-sm">支持 JPG、PNG、WEBP 格式，单张图片不超过 10MB</p>
      </div>
    </div>

    <!-- 抠图设置 -->
    <div v-if="selectedImages.length > 0" class="p-6 border-t border-gray-100 dark:border-dark-border">
      <h4 class="text-lg font-semibold text-gray-900 dark:text-dark-text mb-4">抠图设置</h4>
      <div class="grid grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-dark-text mb-2">抠图模式</label>
          <el-select v-model="cutoutMode" placeholder="选择抠图模式" style="width: 100%">
            <el-option label="智能抠图" value="smart" />
            <el-option label="人像抠图" value="portrait" />
            <el-option label="物体抠图" value="object" />
            <el-option label="精细抠图" value="precise" />
          </el-select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-dark-text mb-2">输出格式</label>
          <el-select v-model="outputFormat" placeholder="选择输出格式" style="width: 100%">
            <el-option label="PNG (透明背景)" value="png" />
            <el-option label="JPG (白色背景)" value="jpg" />
            <el-option label="WEBP (透明背景)" value="webp" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="flex justify-end space-x-3 p-6">
        <button @click="handleClose"
          class="px-6 py-2.5 border border-gray-300 dark:border-dark-border text-gray-700 dark:text-dark-text rounded-lg hover:bg-gray-50 dark:hover:bg-dark-surface transition-all duration-200">
          取消
        </button>
        <button @click="createTask" :disabled="selectedImages.length === 0"
          class="px-6 py-2.5 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 disabled:from-gray-300 disabled:to-gray-400 text-white rounded-lg shadow-lg hover:shadow-xl disabled:shadow-none transition-all duration-200 transform hover:scale-105 disabled:transform-none disabled:cursor-not-allowed">
          开始抠图 ({{ selectedImages.length }})
        </button>
      </div>
    </template>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept="image/*"
      @change="handleFileSelect"
      class="hidden"
    />

    <!-- 图库选择弹窗 -->
    <GallerySelectDialog
      v-model="showGalleryDialog"
      @select="handleGallerySelect"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import GallerySelectDialog from './GallerySelectDialog.vue';

// Props
const props = defineProps<{
  modelValue: boolean;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'success': [];
}>();

// 类型定义
interface ImageFile {
  id?: string;
  name: string;
  url: string;
  file?: File;
}

// 响应式数据
const fileInput = ref<HTMLInputElement>();
const showGalleryDialog = ref(false);
const selectedImages = ref<ImageFile[]>([]);
const cutoutMode = ref('smart');
const outputFormat = ref('png');

// 分页相关
const currentPage = ref(1);
const pageSize = ref(18);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const totalPages = computed(() => Math.ceil(selectedImages.value.length / pageSize.value));

const currentPageImages = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return selectedImages.value.slice(start, end);
});

// 方法
const triggerUpload = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (files) {
    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file);
        selectedImages.value.push({
          name: file.name,
          url,
          file
        });
      }
    });
  }
  // 清空input值，允许重复选择同一文件
  target.value = '';
};

const handleGallerySelect = (images: ImageFile[]) => {
  selectedImages.value.push(...images);
};

const removeImage = (index: number) => {
  const actualIndex = (currentPage.value - 1) * pageSize.value + index;
  const image = selectedImages.value[actualIndex];
  if (image.url.startsWith('blob:')) {
    URL.revokeObjectURL(image.url);
  }
  selectedImages.value.splice(actualIndex, 1);
  
  // 如果当前页没有图片了，回到上一页
  if (currentPageImages.value.length === 0 && currentPage.value > 1) {
    currentPage.value--;
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const createTask = () => {
  if (selectedImages.value.length === 0) {
    ElMessage.warning('请先选择要抠图的图片');
    return;
  }

  ElMessage.success(`正在创建抠图任务，共 ${selectedImages.value.length} 张图片`);
  
  // 清理对象URL
  selectedImages.value.forEach(image => {
    if (image.url.startsWith('blob:')) {
      URL.revokeObjectURL(image.url);
    }
  });
  
  // 重置表单
  selectedImages.value = [];
  cutoutMode.value = 'smart';
  outputFormat.value = 'png';
  currentPage.value = 1;
  
  emit('success');
  emit('update:modelValue', false);
};

const handleClose = () => {
  // 清理对象URL
  selectedImages.value.forEach(image => {
    if (image.url.startsWith('blob:')) {
      URL.revokeObjectURL(image.url);
    }
  });
  
  // 重置表单
  selectedImages.value = [];
  cutoutMode.value = 'smart';
  outputFormat.value = 'png';
  currentPage.value = 1;
  
  emit('update:modelValue', false);
};
</script>

<style scoped>
.modern-dialog :deep(.el-dialog__header) {
  padding: 0;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.modern-dialog :deep(.el-dialog__footer) {
  padding: 0;
}
</style>
