<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="从图库选择图片" 
    width="1000px" 
    align-center>
    <div class="space-y-4">
      <!-- 搜索和筛选 -->
      <div class="flex justify-between items-center">
        <div class="flex space-x-4">
          <el-input 
            v-model="searchKeyword" 
            placeholder="搜索图片..." 
            style="width: 300px"
            @input="handleSearch">
            <template #prefix>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </template>
          </el-input>
          
          <el-select v-model="selectedCategory" placeholder="选择分类" style="width: 150px" @change="handleCategoryChange">
            <el-option label="全部" value="" />
            <el-option label="商品图片" value="product" />
            <el-option label="背景图片" value="background" />
            <el-option label="素材图片" value="material" />
          </el-select>
        </div>
        
        <div class="text-sm text-gray-600 dark:text-dark-text-secondary">
          已选择 {{ selectedImages.length }} 张图片
        </div>
      </div>

      <!-- 图片网格 -->
      <div class="border border-gray-200 dark:border-dark-border rounded-lg p-4 max-h-96 overflow-y-auto">
        <div class="grid grid-cols-6 gap-4">
          <div v-for="image in filteredImages" :key="image.id" 
               class="relative group cursor-pointer"
               @click="toggleImageSelection(image)">
            <div class="relative">
              <img :src="image.thumbnail" :alt="image.name" 
                   class="w-full h-24 object-cover rounded-lg border-2 transition-all duration-200"
                   :class="isSelected(image) ? 'border-green-500' : 'border-gray-200 dark:border-dark-border hover:border-green-300'">
              
              <!-- 选中状态 -->
              <div v-if="isSelected(image)" 
                   class="absolute inset-0 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>
              
              <!-- 悬停效果 -->
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200"></div>
            </div>
            
            <!-- 图片信息 -->
            <div class="mt-1 text-xs text-gray-600 dark:text-dark-text-secondary truncate">
              {{ image.name }}
            </div>
            <div class="text-xs text-gray-400">
              {{ image.size }}
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredImages.length === 0" class="text-center py-12">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <p class="text-gray-500 dark:text-dark-text-secondary">没有找到匹配的图片</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalImages"
          layout="prev, pager, next"
          @current-change="handlePageChange" />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <el-button @click="selectAll" v-if="filteredImages.length > 0">
          {{ isAllSelected ? '取消全选' : '全选当页' }}
        </el-button>
        <div class="flex space-x-3">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelection" :disabled="selectedImages.length === 0">
            确认选择 ({{ selectedImages.length }})
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// Props
const props = defineProps<{
  modelValue: boolean;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'select': [images: any[]];
}>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const searchKeyword = ref('');
const selectedCategory = ref('');
const selectedImages = ref([]);
const currentPage = ref(1);
const pageSize = ref(24);
const totalImages = ref(0);

// 模拟图库数据
const galleryImages = ref([
  {
    id: 1,
    name: 'product-1.jpg',
    thumbnail: 'https://via.placeholder.com/150x150/4ade80/ffffff?text=P1',
    url: 'https://via.placeholder.com/800x800/4ade80/ffffff?text=Product+1',
    size: '800x800',
    category: 'product'
  },
  {
    id: 2,
    name: 'product-2.jpg',
    thumbnail: 'https://via.placeholder.com/150x150/06b6d4/ffffff?text=P2',
    url: 'https://via.placeholder.com/800x800/06b6d4/ffffff?text=Product+2',
    size: '1200x800',
    category: 'product'
  },
  {
    id: 3,
    name: 'background-1.jpg',
    thumbnail: 'https://via.placeholder.com/150x150/8b5cf6/ffffff?text=B1',
    url: 'https://via.placeholder.com/800x800/8b5cf6/ffffff?text=Background+1',
    size: '1920x1080',
    category: 'background'
  },
  {
    id: 4,
    name: 'material-1.jpg',
    thumbnail: 'https://via.placeholder.com/150x150/f59e0b/ffffff?text=M1',
    url: 'https://via.placeholder.com/800x800/f59e0b/ffffff?text=Material+1',
    size: '600x400',
    category: 'material'
  },
  {
    id: 5,
    name: 'product-3.jpg',
    thumbnail: 'https://via.placeholder.com/150x150/ef4444/ffffff?text=P3',
    url: 'https://via.placeholder.com/800x800/ef4444/ffffff?text=Product+3',
    size: '1000x1000',
    category: 'product'
  },
  {
    id: 6,
    name: 'background-2.jpg',
    thumbnail: 'https://via.placeholder.com/150x150/10b981/ffffff?text=B2',
    url: 'https://via.placeholder.com/800x800/10b981/ffffff?text=Background+2',
    size: '1600x900',
    category: 'background'
  }
]);

// 计算属性
const filteredImages = computed(() => {
  let filtered = galleryImages.value;
  
  // 按分类筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(img => img.category === selectedCategory.value);
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    filtered = filtered.filter(img => 
      img.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  
  totalImages.value = filtered.length;
  return filtered.slice(start, end);
});

const isAllSelected = computed(() => {
  return filteredImages.value.length > 0 && 
         filteredImages.value.every(img => isSelected(img));
});

// 方法
const isSelected = (image: any) => {
  return selectedImages.value.some(selected => selected.id === image.id);
};

const toggleImageSelection = (image: any) => {
  const index = selectedImages.value.findIndex(selected => selected.id === image.id);
  if (index > -1) {
    selectedImages.value.splice(index, 1);
  } else {
    selectedImages.value.push(image);
  }
};

const selectAll = () => {
  if (isAllSelected.value) {
    // 取消选择当页所有图片
    filteredImages.value.forEach(img => {
      const index = selectedImages.value.findIndex(selected => selected.id === img.id);
      if (index > -1) {
        selectedImages.value.splice(index, 1);
      }
    });
  } else {
    // 选择当页所有图片
    filteredImages.value.forEach(img => {
      if (!isSelected(img)) {
        selectedImages.value.push(img);
      }
    });
  }
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handleCategoryChange = () => {
  currentPage.value = 1;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const confirmSelection = () => {
  emit('select', selectedImages.value);
  selectedImages.value = [];
};

onMounted(() => {
  // 初始化数据
});
</script>
