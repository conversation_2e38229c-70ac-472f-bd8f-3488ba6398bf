{"name": "riinai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@heroicons/vue": "^2.2.0", "element-plus": "^2.10.4", "vue": "^3.4.21", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.17", "postcss": "^8.4.31", "tailwindcss": "3.4.1", "typescript": "^5.2.2", "vite": "^5.2.0", "vue-tsc": "^2.0.6"}}